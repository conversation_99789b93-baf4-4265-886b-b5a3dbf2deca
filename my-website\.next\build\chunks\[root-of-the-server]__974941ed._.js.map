{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/globals.ts"], "sourcesContent": ["// @ts-ignore\r\nprocess.turbopack = {}\r\n"], "names": [], "mappings": "AAAA,aAAa;AACb,QAAQ,SAAS,GAAG,CAAC"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/compiled/stacktrace-parser/index.js"], "sourcesContent": ["if (typeof __nccwpck_require__ !== \"undefined\")\r\n  __nccwpck_require__.ab = __dirname + \"/\";\r\n\r\nvar n = \"<unknown>\";\r\nexport function parse(e) {\r\n  var r = e.split(\"\\n\");\r\n  return r.reduce(function (e, r) {\r\n    var n =\r\n      parseChrome(r) ||\r\n      parseWinjs(r) ||\r\n      parseGecko(r) ||\r\n      parseNode(r) ||\r\n      parseJSC(r);\r\n    if (n) {\r\n      e.push(n);\r\n    }\r\n    return e;\r\n  }, []);\r\n}\r\nvar a =\r\n  /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\r\nvar l = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\r\nfunction parseChrome(e) {\r\n  var r = a.exec(e);\r\n  if (!r) {\r\n    return null;\r\n  }\r\n  var u = r[2] && r[2].indexOf(\"native\") === 0;\r\n  var t = r[2] && r[2].indexOf(\"eval\") === 0;\r\n  var i = l.exec(r[2]);\r\n  if (t && i != null) {\r\n    r[2] = i[1];\r\n    r[3] = i[2];\r\n    r[4] = i[3];\r\n  }\r\n  return {\r\n    file: !u ? r[2] : null,\r\n    methodName: r[1] || n,\r\n    arguments: u ? [r[2]] : [],\r\n    lineNumber: r[3] ? +r[3] : null,\r\n    column: r[4] ? +r[4] : null,\r\n  };\r\n}\r\nvar u =\r\n  /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\r\nfunction parseWinjs(e) {\r\n  var r = u.exec(e);\r\n  if (!r) {\r\n    return null;\r\n  }\r\n  return {\r\n    file: r[2],\r\n    methodName: r[1] || n,\r\n    arguments: [],\r\n    lineNumber: +r[3],\r\n    column: r[4] ? +r[4] : null,\r\n  };\r\n}\r\nvar t =\r\n  /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\r\nvar i = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\r\nfunction parseGecko(e) {\r\n  var r = t.exec(e);\r\n  if (!r) {\r\n    return null;\r\n  }\r\n  var a = r[3] && r[3].indexOf(\" > eval\") > -1;\r\n  var l = i.exec(r[3]);\r\n  if (a && l != null) {\r\n    r[3] = l[1];\r\n    r[4] = l[2];\r\n    r[5] = null;\r\n  }\r\n  return {\r\n    file: r[3],\r\n    methodName: r[1] || n,\r\n    arguments: r[2] ? r[2].split(\",\") : [],\r\n    lineNumber: r[4] ? +r[4] : null,\r\n    column: r[5] ? +r[5] : null,\r\n  };\r\n}\r\nvar s = /^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;\r\nfunction parseJSC(e) {\r\n  var r = s.exec(e);\r\n  if (!r) {\r\n    return null;\r\n  }\r\n  return {\r\n    file: r[3],\r\n    methodName: r[1] || n,\r\n    arguments: [],\r\n    lineNumber: +r[4],\r\n    column: r[5] ? +r[5] : null,\r\n  };\r\n}\r\nvar o =\r\n  /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\r\nfunction parseNode(e) {\r\n  var r = o.exec(e);\r\n  if (!r) {\r\n    return null;\r\n  }\r\n  return {\r\n    file: r[2],\r\n    methodName: r[1] || n,\r\n    arguments: [],\r\n    lineNumber: +r[3],\r\n    column: r[4] ? +r[4] : null,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA,IAAI,OAAO,wBAAwB,aACjC,oBAAoB,EAAE,GAAG,uEAAY;AAEvC,IAAI,IAAI;AACD,SAAS,MAAM,CAAC;IACrB,IAAI,IAAI,EAAE,KAAK,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC,SAAU,CAAC,EAAE,CAAC;QAC5B,IAAI,IACF,YAAY,MACZ,WAAW,MACX,WAAW,MACX,UAAU,MACV,SAAS;QACX,IAAI,GAAG;YACL,EAAE,IAAI,CAAC;QACT;QACA,OAAO;IACT,GAAG,EAAE;AACP;AACA,IAAI,IACF;AACF,IAAI,IAAI;AACR,SAAS,YAAY,CAAC;IACpB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,cAAc;IAC3C,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,YAAY;IACzC,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IACnB,IAAI,KAAK,KAAK,MAAM;QAClB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACb;IACA,OAAO;QACL,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG;QAClB,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,IAAI;YAAC,CAAC,CAAC,EAAE;SAAC,GAAG,EAAE;QAC1B,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;QAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,SAAS,WAAW,CAAC;IACnB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,IAAI,IAAI;AACR,SAAS,WAAW,CAAC;IACnB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC;IAC3C,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE;IACnB,IAAI,KAAK,KAAK,MAAM;QAClB,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QACX,CAAC,CAAC,EAAE,GAAG;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE;QACtC,YAAY,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;QAC3B,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IAAI;AACR,SAAS,SAAS,CAAC;IACjB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF;AACA,IAAI,IACF;AACF,SAAS,UAAU,CAAC;IAClB,IAAI,IAAI,EAAE,IAAI,CAAC;IACf,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM,CAAC,CAAC,EAAE;QACV,YAAY,CAAC,CAAC,EAAE,IAAI;QACpB,WAAW,EAAE;QACb,YAAY,CAAC,CAAC,CAAC,EAAE;QACjB,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG;IACzB;AACF"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/error.ts"], "sourcesContent": ["// merged from next.js\r\n// https://github.com/vercel/next.js/blob/e657741b9908cf0044aaef959c0c4defb19ed6d8/packages/next/src/lib/is-error.ts\r\n// https://github.com/vercel/next.js/blob/e657741b9908cf0044aaef959c0c4defb19ed6d8/packages/next/src/shared/lib/is-plain-object.ts\r\n\r\nexport default function isError(err: unknown): err is Error {\r\n  return (\r\n    typeof err === 'object' && err !== null && 'name' in err && 'message' in err\r\n  )\r\n}\r\n\r\nexport function getProperError(err: unknown): Error {\r\n  if (isError(err)) {\r\n    return err\r\n  }\r\n\r\n  if (process.env.NODE_ENV === 'development') {\r\n    // Provide a better error message for cases where `throw undefined`\r\n    // is called in development\r\n    if (typeof err === 'undefined') {\r\n      return new Error('`undefined` was thrown instead of a real error')\r\n    }\r\n\r\n    if (err === null) {\r\n      return new Error('`null` was thrown instead of a real error')\r\n    }\r\n  }\r\n\r\n  return new Error(isPlainObject(err) ? JSON.stringify(err) : err + '')\r\n}\r\n\r\nfunction getObjectClassLabel(value: any): string {\r\n  return Object.prototype.toString.call(value)\r\n}\r\n\r\nfunction isPlainObject(value: any): boolean {\r\n  if (getObjectClassLabel(value) !== '[object Object]') {\r\n    return false\r\n  }\r\n\r\n  const prototype = Object.getPrototypeOf(value)\r\n\r\n  /**\r\n   * this used to be previously:\r\n   *\r\n   * `return prototype === null || prototype === Object.prototype`\r\n   *\r\n   * but Edge Runtime expose Object from vm, being that kind of type-checking wrongly fail.\r\n   *\r\n   * It was changed to the current implementation since it's resilient to serialization.\r\n   */\r\n  return prototype === null || prototype.hasOwnProperty('isPrototypeOf')\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;AACtB,oHAAoH;AACpH,kIAAkI;;;;;;;AAEnH,SAAS,QAAQ,GAAY;IAC1C,OACE,OAAO,QAAQ,YAAY,QAAQ,QAAQ,UAAU,OAAO,aAAa;AAE7E;AAEO,SAAS,eAAe,GAAY;IACzC,IAAI,QAAQ,MAAM;QAChB,OAAO;IACT;IAEA,wCAA4C;QAC1C,mEAAmE;QACnE,2BAA2B;QAC3B,IAAI,OAAO,QAAQ,aAAa;YAC9B,OAAO,IAAI,MAAM;QACnB;QAEA,IAAI,QAAQ,MAAM;YAChB,OAAO,IAAI,MAAM;QACnB;IACF;IAEA,OAAO,IAAI,MAAM,cAAc,OAAO,KAAK,SAAS,CAAC,OAAO,MAAM;AACpE;AAEA,SAAS,oBAAoB,KAAU;IACrC,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC;AACxC;AAEA,SAAS,cAAc,KAAU;IAC/B,IAAI,oBAAoB,WAAW,mBAAmB;QACpD,OAAO;IACT;IAEA,MAAM,YAAY,OAAO,cAAc,CAAC;IAExC;;;;;;;;GAQC,GACD,OAAO,cAAc,QAAQ,UAAU,cAAc,CAAC;AACxD"}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/index.ts"], "sourcesContent": ["import { createConnection } from 'node:net'\r\nimport { Writable } from 'node:stream'\r\nimport type { StackFrame } from '../compiled/stacktrace-parser'\r\nimport { parse as parseStackTrace } from '../compiled/stacktrace-parser'\r\nimport { getProperError } from './error'\r\n\r\nexport type StructuredError = {\r\n  name: string\r\n  message: string\r\n  stack: StackFrame[]\r\n  cause: StructuredError | undefined\r\n}\r\n\r\nexport function structuredError(e: unknown): StructuredError {\r\n  e = getProperError(e)\r\n\r\n  return {\r\n    name: e.name,\r\n    message: e.message,\r\n    stack: typeof e.stack === 'string' ? parseStackTrace(e.stack) : [],\r\n    cause: e.cause ? structuredError(getProperError(e.cause)) : undefined,\r\n  }\r\n}\r\n\r\ntype State =\r\n  | {\r\n      type: 'waiting'\r\n    }\r\n  | {\r\n      type: 'packet'\r\n      length: number\r\n    }\r\n\r\nexport type Ipc<TIncoming, TOutgoing> = {\r\n  recv(): Promise<TIncoming>\r\n  send(message: TOutgoing): Promise<void>\r\n  sendError(error: Error | string): Promise<never>\r\n  sendReady(): Promise<void>\r\n}\r\n\r\nfunction createIpc<TIncoming, TOutgoing>(\r\n  port: number\r\n): Ipc<TIncoming, TOutgoing> {\r\n  const socket = createConnection({\r\n    port,\r\n    host: '127.0.0.1',\r\n  })\r\n\r\n  /**\r\n   * A writable stream that writes to the socket.\r\n   * We don't write directly to the socket because we need to\r\n   * handle backpressure and wait for the socket to be drained\r\n   * before writing more data.\r\n   */\r\n  const socketWritable = new Writable({\r\n    write(chunk, _enc, cb) {\r\n      if (socket.write(chunk)) {\r\n        cb()\r\n      } else {\r\n        socket.once('drain', cb)\r\n      }\r\n    },\r\n    final(cb) {\r\n      socket.end(cb)\r\n    },\r\n  })\r\n\r\n  const packetQueue: Buffer[] = []\r\n  const recvPromiseResolveQueue: Array<(message: TIncoming) => void> = []\r\n\r\n  function pushPacket(packet: Buffer) {\r\n    const recvPromiseResolve = recvPromiseResolveQueue.shift()\r\n    if (recvPromiseResolve != null) {\r\n      recvPromiseResolve(JSON.parse(packet.toString('utf8')) as TIncoming)\r\n    } else {\r\n      packetQueue.push(packet)\r\n    }\r\n  }\r\n\r\n  let state: State = { type: 'waiting' }\r\n  let buffer: Buffer = Buffer.alloc(0)\r\n  socket.once('connect', () => {\r\n    socket.on('data', (chunk) => {\r\n      buffer = Buffer.concat([buffer, chunk])\r\n\r\n      loop: while (true) {\r\n        switch (state.type) {\r\n          case 'waiting': {\r\n            if (buffer.length >= 4) {\r\n              const length = buffer.readUInt32BE(0)\r\n              buffer = buffer.subarray(4)\r\n              state = { type: 'packet', length }\r\n            } else {\r\n              break loop\r\n            }\r\n            break\r\n          }\r\n          case 'packet': {\r\n            if (buffer.length >= state.length) {\r\n              const packet = buffer.subarray(0, state.length)\r\n              buffer = buffer.subarray(state.length)\r\n              state = { type: 'waiting' }\r\n              pushPacket(packet)\r\n            } else {\r\n              break loop\r\n            }\r\n            break\r\n          }\r\n          default:\r\n            invariant(state, (state) => `Unknown state type: ${state?.type}`)\r\n        }\r\n      }\r\n    })\r\n  })\r\n  // When the socket is closed, this process is no longer needed.\r\n  // This might happen e. g. when parent process is killed or\r\n  // node.js pool is garbage collected.\r\n  socket.once('close', () => {\r\n    process.exit(0)\r\n  })\r\n\r\n  // TODO(lukesandberg): some of the messages being sent are very large and contain lots\r\n  //  of redundant information.  Consider adding gzip compression to our stream.\r\n  function doSend(message: string): Promise<void> {\r\n    return new Promise((resolve, reject) => {\r\n      // Reserve 4 bytes for our length prefix, we will over-write after encoding.\r\n      const packet = Buffer.from('0000' + message, 'utf8')\r\n      packet.writeUInt32BE(packet.length - 4, 0)\r\n      socketWritable.write(packet, (err) => {\r\n        process.stderr.write(`TURBOPACK_OUTPUT_D\\n`)\r\n        process.stdout.write(`TURBOPACK_OUTPUT_D\\n`)\r\n        if (err != null) {\r\n          reject(err)\r\n        } else {\r\n          resolve()\r\n        }\r\n      })\r\n    })\r\n  }\r\n\r\n  function send(message: any): Promise<void> {\r\n    return doSend(JSON.stringify(message))\r\n  }\r\n  function sendReady(): Promise<void> {\r\n    return doSend('')\r\n  }\r\n\r\n  return {\r\n    async recv() {\r\n      const packet = packetQueue.shift()\r\n      if (packet != null) {\r\n        return JSON.parse(packet.toString('utf8')) as TIncoming\r\n      }\r\n\r\n      const result = await new Promise<TIncoming>((resolve) => {\r\n        recvPromiseResolveQueue.push((result) => {\r\n          resolve(result)\r\n        })\r\n      })\r\n\r\n      return result\r\n    },\r\n\r\n    send(message: TOutgoing) {\r\n      return send(message)\r\n    },\r\n\r\n    sendReady,\r\n\r\n    async sendError(error: Error): Promise<never> {\r\n      try {\r\n        await send({\r\n          type: 'error',\r\n          ...structuredError(error),\r\n        })\r\n      } catch (err) {\r\n        console.error('failed to send error back to rust:', err)\r\n        // ignore and exit anyway\r\n        process.exit(1)\r\n      }\r\n\r\n      process.exit(0)\r\n    },\r\n  }\r\n}\r\n\r\nconst PORT = process.argv[2]\r\n\r\nexport const IPC = createIpc<unknown, unknown>(parseInt(PORT, 10))\r\n\r\nprocess.on('uncaughtException', (err) => {\r\n  IPC.sendError(err)\r\n})\r\n\r\nconst improveConsole = (name: string, stream: string, addStack: boolean) => {\r\n  // @ts-ignore\r\n  const original = console[name]\r\n  // @ts-ignore\r\n  const stdio = process[stream]\r\n  // @ts-ignore\r\n  console[name] = (...args: any[]) => {\r\n    stdio.write(`TURBOPACK_OUTPUT_B\\n`)\r\n    original(...args)\r\n    if (addStack) {\r\n      const stack = new Error().stack?.replace(/^.+\\n.+\\n/, '') + '\\n'\r\n      stdio.write('TURBOPACK_OUTPUT_S\\n')\r\n      stdio.write(stack)\r\n    }\r\n    stdio.write('TURBOPACK_OUTPUT_E\\n')\r\n  }\r\n}\r\n\r\nimproveConsole('error', 'stderr', true)\r\nimproveConsole('warn', 'stderr', true)\r\nimproveConsole('count', 'stdout', true)\r\nimproveConsole('trace', 'stderr', false)\r\nimproveConsole('log', 'stdout', true)\r\nimproveConsole('group', 'stdout', true)\r\nimproveConsole('groupCollapsed', 'stdout', true)\r\nimproveConsole('table', 'stdout', true)\r\nimproveConsole('debug', 'stdout', true)\r\nimproveConsole('info', 'stdout', true)\r\nimproveConsole('dir', 'stdout', true)\r\nimproveConsole('dirxml', 'stdout', true)\r\nimproveConsole('timeEnd', 'stdout', true)\r\nimproveConsole('timeLog', 'stdout', true)\r\nimproveConsole('timeStamp', 'stdout', true)\r\nimproveConsole('assert', 'stderr', true)\r\n\r\n/**\r\n * Utility function to ensure all variants of an enum are handled.\r\n */\r\nfunction invariant(never: never, computeMessage: (arg: any) => string): never {\r\n  throw new Error(`Invariant: ${computeMessage(never)}`)\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;AACA;;;;;AASO,SAAS,gBAAgB,CAAU;IACxC,IAAI,IAAA,qIAAc,EAAC;IAEnB,OAAO;QACL,MAAM,EAAE,IAAI;QACZ,SAAS,EAAE,OAAO;QAClB,OAAO,OAAO,EAAE,KAAK,KAAK,WAAW,IAAA,yJAAe,EAAC,EAAE,KAAK,IAAI,EAAE;QAClE,OAAO,EAAE,KAAK,GAAG,gBAAgB,IAAA,qIAAc,EAAC,EAAE,KAAK,KAAK;IAC9D;AACF;AAkBA,SAAS,UACP,IAAY;IAEZ,MAAM,SAAS,IAAA,mIAAgB,EAAC;QAC9B;QACA,MAAM;IACR;IAEA;;;;;GAKC,GACD,MAAM,iBAAiB,IAAI,iIAAQ,CAAC;QAClC,OAAM,KAAK,EAAE,IAAI,EAAE,EAAE;YACnB,IAAI,OAAO,KAAK,CAAC,QAAQ;gBACvB;YACF,OAAO;gBACL,OAAO,IAAI,CAAC,SAAS;YACvB;QACF;QACA,OAAM,EAAE;YACN,OAAO,GAAG,CAAC;QACb;IACF;IAEA,MAAM,cAAwB,EAAE;IAChC,MAAM,0BAA+D,EAAE;IAEvE,SAAS,WAAW,MAAc;QAChC,MAAM,qBAAqB,wBAAwB,KAAK;QACxD,IAAI,sBAAsB,MAAM;YAC9B,mBAAmB,KAAK,KAAK,CAAC,OAAO,QAAQ,CAAC;QAChD,OAAO;YACL,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,IAAI,QAAe;QAAE,MAAM;IAAU;IACrC,IAAI,SAAiB,OAAO,KAAK,CAAC;IAClC,OAAO,IAAI,CAAC,WAAW;QACrB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACjB,SAAS,OAAO,MAAM,CAAC;gBAAC;gBAAQ;aAAM;YAEtC,MAAM,MAAO,KAAM;gBACjB,OAAQ,MAAM,IAAI;oBAChB,KAAK;wBAAW;4BACd,IAAI,OAAO,MAAM,IAAI,GAAG;gCACtB,MAAM,SAAS,OAAO,YAAY,CAAC;gCACnC,SAAS,OAAO,QAAQ,CAAC;gCACzB,QAAQ;oCAAE,MAAM;oCAAU;gCAAO;4BACnC,OAAO;gCACL,MAAM;4BACR;4BACA;wBACF;oBACA,KAAK;wBAAU;4BACb,IAAI,OAAO,MAAM,IAAI,MAAM,MAAM,EAAE;gCACjC,MAAM,SAAS,OAAO,QAAQ,CAAC,GAAG,MAAM,MAAM;gCAC9C,SAAS,OAAO,QAAQ,CAAC,MAAM,MAAM;gCACrC,QAAQ;oCAAE,MAAM;gCAAU;gCAC1B,WAAW;4BACb,OAAO;gCACL,MAAM;4BACR;4BACA;wBACF;oBACA;wBACE,UAAU,OAAO,CAAC,QAAU,CAAC,oBAAoB,EAAE,OAAO,MAAM;gBACpE;YACF;QACF;IACF;IACA,+DAA+D;IAC/D,2DAA2D;IAC3D,qCAAqC;IACrC,OAAO,IAAI,CAAC,SAAS;QACnB,QAAQ,IAAI,CAAC;IACf;IAEA,sFAAsF;IACtF,8EAA8E;IAC9E,SAAS,OAAO,OAAe;QAC7B,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,4EAA4E;YAC5E,MAAM,SAAS,OAAO,IAAI,CAAC,SAAS,SAAS;YAC7C,OAAO,aAAa,CAAC,OAAO,MAAM,GAAG,GAAG;YACxC,eAAe,KAAK,CAAC,QAAQ,CAAC;gBAC5B,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,oBAAoB,CAAC;gBAC3C,IAAI,OAAO,MAAM;oBACf,OAAO;gBACT,OAAO;oBACL;gBACF;YACF;QACF;IACF;IAEA,SAAS,KAAK,OAAY;QACxB,OAAO,OAAO,KAAK,SAAS,CAAC;IAC/B;IACA,SAAS;QACP,OAAO,OAAO;IAChB;IAEA,OAAO;QACL,MAAM;YACJ,MAAM,SAAS,YAAY,KAAK;YAChC,IAAI,UAAU,MAAM;gBAClB,OAAO,KAAK,KAAK,CAAC,OAAO,QAAQ,CAAC;YACpC;YAEA,MAAM,SAAS,MAAM,IAAI,QAAmB,CAAC;gBAC3C,wBAAwB,IAAI,CAAC,CAAC;oBAC5B,QAAQ;gBACV;YACF;YAEA,OAAO;QACT;QAEA,MAAK,OAAkB;YACrB,OAAO,KAAK;QACd;QAEA;QAEA,MAAM,WAAU,KAAY;YAC1B,IAAI;gBACF,MAAM,KAAK;oBACT,MAAM;oBACN,GAAG,gBAAgB,MAAM;gBAC3B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,yBAAyB;gBACzB,QAAQ,IAAI,CAAC;YACf;YAEA,QAAQ,IAAI,CAAC;QACf;IACF;AACF;AAEA,MAAM,OAAO,QAAQ,IAAI,CAAC,EAAE;AAErB,MAAM,MAAM,UAA4B,SAAS,MAAM;AAE9D,QAAQ,EAAE,CAAC,qBAAqB,CAAC;IAC/B,IAAI,SAAS,CAAC;AAChB;AAEA,MAAM,iBAAiB,CAAC,MAAc,QAAgB;IACpD,aAAa;IACb,MAAM,WAAW,OAAO,CAAC,KAAK;IAC9B,aAAa;IACb,MAAM,QAAQ,OAAO,CAAC,OAAO;IAC7B,aAAa;IACb,OAAO,CAAC,KAAK,GAAG,CAAC,GAAG;QAClB,MAAM,KAAK,CAAC,CAAC,oBAAoB,CAAC;QAClC,YAAY;QACZ,IAAI,UAAU;YACZ,MAAM,QAAQ,IAAI,QAAQ,KAAK,EAAE,QAAQ,aAAa,MAAM;YAC5D,MAAM,KAAK,CAAC;YACZ,MAAM,KAAK,CAAC;QACd;QACA,MAAM,KAAK,CAAC;IACd;AACF;AAEA,eAAe,SAAS,UAAU;AAClC,eAAe,QAAQ,UAAU;AACjC,eAAe,SAAS,UAAU;AAClC,eAAe,SAAS,UAAU;AAClC,eAAe,OAAO,UAAU;AAChC,eAAe,SAAS,UAAU;AAClC,eAAe,kBAAkB,UAAU;AAC3C,eAAe,SAAS,UAAU;AAClC,eAAe,SAAS,UAAU;AAClC,eAAe,QAAQ,UAAU;AACjC,eAAe,OAAO,UAAU;AAChC,eAAe,UAAU,UAAU;AACnC,eAAe,WAAW,UAAU;AACpC,eAAe,WAAW,UAAU;AACpC,eAAe,aAAa,UAAU;AACtC,eAAe,UAAU,UAAU;AAEnC;;CAEC,GACD,SAAS,UAAU,KAAY,EAAE,cAAoC;IACnE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,eAAe,QAAQ;AACvD"}}, {"offset": {"line": 390, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/evaluate.ts"], "sourcesContent": ["import { IPC } from './index'\r\nimport type { Ipc as GenericIpc } from './index'\r\n\r\ntype IpcIncomingMessage =\r\n  | {\r\n      type: 'evaluate'\r\n      args: string[]\r\n    }\r\n  | {\r\n      type: 'result'\r\n      id: number\r\n      error: string | null\r\n      data: any | null\r\n    }\r\n\r\ntype IpcOutgoingMessage =\r\n  | {\r\n      type: 'end'\r\n      data: string | undefined\r\n      duration: number\r\n    }\r\n  | {\r\n      type: 'info'\r\n      data: any\r\n    }\r\n  | {\r\n      type: 'request'\r\n      id: number\r\n      data: any\r\n    }\r\n\r\nexport type Ipc<IM, RM> = {\r\n  sendInfo(message: IM): Promise<void>\r\n  sendRequest(message: RM): Promise<unknown>\r\n  sendError(error: Error): Promise<never>\r\n}\r\nconst ipc = IPC as GenericIpc<IpcIncomingMessage, IpcOutgoingMessage>\r\n\r\nconst queue: string[][] = []\r\n\r\nexport const run = async (\r\n  moduleFactory: () => Promise<{\r\n    init?: () => Promise<void>\r\n    default: (ipc: Ipc<any, any>, ...deserializedArgs: any[]) => any\r\n  }>\r\n) => {\r\n  let nextId = 1\r\n  const requests = new Map()\r\n  const internalIpc = {\r\n    sendInfo: (message: any) =>\r\n      ipc.send({\r\n        type: 'info',\r\n        data: message,\r\n      }),\r\n    sendRequest: (message: any) => {\r\n      const id = nextId++\r\n      let resolve, reject\r\n      const promise = new Promise((res, rej) => {\r\n        resolve = res\r\n        reject = rej\r\n      })\r\n      requests.set(id, { resolve, reject })\r\n      return ipc\r\n        .send({ type: 'request', id, data: message })\r\n        .then(() => promise)\r\n    },\r\n    sendError: (error: Error) => {\r\n      return ipc.sendError(error)\r\n    },\r\n  }\r\n\r\n  // Initialize module and send ready message\r\n  let getValue: (ipc: Ipc<any, any>, ...deserializedArgs: any[]) => any\r\n  try {\r\n    const module = await moduleFactory()\r\n    if (typeof module.init === 'function') {\r\n      await module.init()\r\n    }\r\n    getValue = module.default\r\n    await ipc.sendReady()\r\n  } catch (err) {\r\n    await ipc.sendReady()\r\n    await ipc.sendError(err as Error)\r\n  }\r\n\r\n  // Queue handling\r\n  let isRunning = false\r\n  const run = async () => {\r\n    while (queue.length > 0) {\r\n      const args = queue.shift()!\r\n      try {\r\n        const value = await getValue(internalIpc, ...args)\r\n        await ipc.send({\r\n          type: 'end',\r\n          data:\r\n            value === undefined ? undefined : JSON.stringify(value, null, 2),\r\n          duration: 0,\r\n        })\r\n      } catch (e) {\r\n        await ipc.sendError(e as Error)\r\n      }\r\n    }\r\n    isRunning = false\r\n  }\r\n\r\n  // Communication handling\r\n  while (true) {\r\n    const msg = await ipc.recv()\r\n\r\n    switch (msg.type) {\r\n      case 'evaluate': {\r\n        queue.push(msg.args)\r\n        if (!isRunning) {\r\n          isRunning = true\r\n          run()\r\n        }\r\n        break\r\n      }\r\n      case 'result': {\r\n        const request = requests.get(msg.id)\r\n        if (request) {\r\n          requests.delete(msg.id)\r\n          if (msg.error) {\r\n            request.reject(new Error(msg.error))\r\n          } else {\r\n            request.resolve(msg.data)\r\n          }\r\n        }\r\n        break\r\n      }\r\n      default: {\r\n        console.error('unexpected message type', (msg as any).type)\r\n        process.exit(1)\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\nexport type { IpcIncomingMessage, IpcOutgoingMessage }\r\n"], "names": [], "mappings": ";;;;AAAA;;AAoCA,MAAM,MAAM,0HAAG;AAEf,MAAM,QAAoB,EAAE;AAErB,MAAM,MAAM,OACjB;IAKA,IAAI,SAAS;IACb,MAAM,WAAW,IAAI;IACrB,MAAM,cAAc;QAClB,UAAU,CAAC,UACT,IAAI,IAAI,CAAC;gBACP,MAAM;gBACN,MAAM;YACR;QACF,aAAa,CAAC;YACZ,MAAM,KAAK;YACX,IAAI,SAAS;YACb,MAAM,UAAU,IAAI,QAAQ,CAAC,KAAK;gBAChC,UAAU;gBACV,SAAS;YACX;YACA,SAAS,GAAG,CAAC,IAAI;gBAAE;gBAAS;YAAO;YACnC,OAAO,IACJ,IAAI,CAAC;gBAAE,MAAM;gBAAW;gBAAI,MAAM;YAAQ,GAC1C,IAAI,CAAC,IAAM;QAChB;QACA,WAAW,CAAC;YACV,OAAO,IAAI,SAAS,CAAC;QACvB;IACF;IAEA,2CAA2C;IAC3C,IAAI;IACJ,IAAI;QACF,MAAM,SAAS,MAAM;QACrB,IAAI,OAAO,OAAO,IAAI,KAAK,YAAY;YACrC,MAAM,OAAO,IAAI;QACnB;QACA,WAAW,OAAO,OAAO;QACzB,MAAM,IAAI,SAAS;IACrB,EAAE,OAAO,KAAK;QACZ,MAAM,IAAI,SAAS;QACnB,MAAM,IAAI,SAAS,CAAC;IACtB;IAEA,iBAAiB;IACjB,IAAI,YAAY;IAChB,MAAM,MAAM;QACV,MAAO,MAAM,MAAM,GAAG,EAAG;YACvB,MAAM,OAAO,MAAM,KAAK;YACxB,IAAI;gBACF,MAAM,QAAQ,MAAM,SAAS,gBAAgB;gBAC7C,MAAM,IAAI,IAAI,CAAC;oBACb,MAAM;oBACN,MACE,UAAU,YAAY,YAAY,KAAK,SAAS,CAAC,OAAO,MAAM;oBAChE,UAAU;gBACZ;YACF,EAAE,OAAO,GAAG;gBACV,MAAM,IAAI,SAAS,CAAC;YACtB;QACF;QACA,YAAY;IACd;IAEA,yBAAyB;IACzB,MAAO,KAAM;QACX,MAAM,MAAM,MAAM,IAAI,IAAI;QAE1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBAAY;oBACf,MAAM,IAAI,CAAC,IAAI,IAAI;oBACnB,IAAI,CAAC,WAAW;wBACd,YAAY;wBACZ;oBACF;oBACA;gBACF;YACA,KAAK;gBAAU;oBACb,MAAM,UAAU,SAAS,GAAG,CAAC,IAAI,EAAE;oBACnC,IAAI,SAAS;wBACX,SAAS,MAAM,CAAC,IAAI,EAAE;wBACtB,IAAI,IAAI,KAAK,EAAE;4BACb,QAAQ,MAAM,CAAC,IAAI,MAAM,IAAI,KAAK;wBACpC,OAAO;4BACL,QAAQ,OAAO,CAAC,IAAI,IAAI;wBAC1B;oBACF;oBACA;gBACF;YACA;gBAAS;oBACP,QAAQ,KAAK,CAAC,2BAA2B,AAAC,IAAY,IAAI;oBAC1D,QAAQ,IAAI,CAAC;gBACf;QACF;IACF;AACF"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/ipc/evaluate.ts/evaluate.js"], "sourcesContent": ["import { run } from 'RUNTIME'; run(() => import('INNER'))"], "names": [], "mappings": ";AAAA;;AAA+B,IAAA,6HAAG,EAAC"}}]}