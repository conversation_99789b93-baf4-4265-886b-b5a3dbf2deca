{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/My-Demo/my-website/src/app/page.tsx"], "sourcesContent": ["import Image from \"next/image\";\nexport default function Home() {\n  return (\n    <div className=\"relative\">\n      <div className=\"absolute insect rounded-lg m-auto border border-(--pattern-fg) bg-[image:repeating-linear-gradient(315deg,_var(--pattern-fg)_0,_var(--pattern-gf)_1px,_1px,_transperent_0,_transperent_50%)] bg-[size:10px_10px] bg-fixed\"></div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;;AACe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}]}